CREATE OR REPLACE FUNCTION build_tier_sms()
RETURNS TEXT AS
$
DECLARE
    v_count INT := 0;
BEGIN
    -- Empty the tier_sms table
    TRUNCATE TABLE subscriber.tier_sms;

    -- Insert the latest record for each customer_phone and vertical
    WITH ranked_sms AS (
        SELECT
            s.emailaddress,
            s.brand_id,
            s.firstname,
            s.lastname,
            s.adid,
            s.pid,
            s.leaddate,
            s.customer_address_1,
            s.customer_address_2,
            s.customer_city,
            s.customer_state,
            s.original_adid,
            s.vertical_type,
            s.zip,
            s.customer_phone,
            s.verification_status,
            s.tcpa_optin,
            s.carrier,
            s.carrierparent,
            s.blacklisted,
            s.status,
            s.type,
            s.shorturl,
            s.pl_shorturl,
            s.jluvr,
            s.lead_id,
            b.vertical,
            ROW_NUMBER() OVER(PARTITION BY s.customer_phone, b.vertical ORDER BY s.leaddate DESC) as rn
        FROM
            subscriber.subscriber_sms s
        JO<PERSON>
            subscriber.brand b ON s.brand_id = b.brand_id
        WHERE
            s.leaddate < date_trunc('day', now()) and s.leaddate >= date_trunc('day', now()) - interval '360 day'
    )
    INSERT INTO subscriber.tier_sms (
        emailaddress,
        brand_id,
        firstname,
        lastname,
        adid,
        pid,
        leaddate,
        customer_address_1,
        customer_address_2,
        customer_city,
        customer_state,
        original_adid,
        vertical_type,
        zip,
        customer_phone,
        verification_status,
        tcpa_optin,
        carrier,
        carrierparent,
        blacklisted,
        status,
        type,
        shorturl,
        pl_shorturl,
        jluvr,
        lead_id
    )
    SELECT
        rs.emailaddress,
        rs.brand_id,
        rs.firstname,
        rs.lastname,
        rs.adid,
        rs.pid,
        rs.leaddate,
        rs.customer_address_1,
        rs.customer_address_2,
        rs.customer_city,
        rs.customer_state,
        rs.original_adid,
        rs.vertical_type,
        rs.zip,
        rs.customer_phone,
        rs.verification_status,
        rs.tcpa_optin,
        rs.carrier,
        rs.carrierparent,
        rs.blacklisted,
        rs.status,
        rs.type,
        rs.shorturl,
        rs.pl_shorturl,
        rs.jluvr,
        rs.lead_id
    FROM
        ranked_sms rs
    WHERE
        rs.rn = 1;

    GET DIAGNOSTICS v_count = ROW_COUNT;

    RETURN CONCAT('Successfully inserted ', v_count, ' row(s) into tier_sms.');

EXCEPTION
    WHEN OTHERS THEN
        RETURN CONCAT('Error: ', SQLERRM);
END;
$
LANGUAGE plpgsql;