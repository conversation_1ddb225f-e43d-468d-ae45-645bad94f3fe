CREATE OR REPLACE FUNCTION auto_routing(
    p_content_type subscriber.content_sms.content_type%TYPE,
    p_platform subscriber.transaction_sms.platform%TYPE,
    p_start_day INT,
    p_end_day INT
)
RETURNS TEXT AS
$$
DECLARE
    v_version subscriber.content_sms.version%TYPE;
    v_message subscriber.content_sms.message%TYPE;
    v_phone subscriber.subscriber_sms.customer_phone%TYPE;
    v_email subscriber.subscriber_sms.emailaddress%TYPE;
    v_firstname subscriber.subscriber_sms.firstname%TYPE;
    v_lastname subscriber.subscriber_sms.lastname%TYPE;
    v_shorturl subscriber.subscriber_sms.shorturl%TYPE;
    v_brand_name subscriber.brand.brand_name%TYPE;
    v_vertical subscriber.brand.vertical%TYPE;
    v_carrier subscriber.subscriber_sms.carrier%TYPE;
    v_did TEXT;
    v_template TEXT;
    v_count INT := 0;

BEGIN
    FOR v_phone, v_email, v_firstname, v_lastname, v_shorturl, v_brand_name, v_vertical, v_carrier IN
        SELECT s.customer_phone, s.emailaddress, s.firstname, s.lastname, s.shorturl,
               b.brand_name, b.vertical, s.carrier
        FROM subscriber.subscriber_sms s
        JOIN subscriber.brand b ON s.brand_id = b.brand_id
        LEFT JOIN subscriber.optout_sms o ON s.customer_phone = o.phone
        WHERE o.phone IS NULL
          AND s.Blacklisted <> 'true'
          AND s.status <> 'not_mobile'
          AND s.leaddate >= CURRENT_DATE - INTERVAL '1 day' * p_start_day
          AND s.leaddate <= CURRENT_DATE - INTERVAL '1 day' * p_end_day
    LOOP
        IF p_platform = 'mce' THEN
            IF v_carrier LIKE '%verizon%' THEN
                v_did := '95064';
                v_template := 'MjA5OTA6Nzg6MA';
            ELSIF v_vertical = 'CRC' THEN
                v_did := '31207';
                v_template := 'MjA5ODk6Nzg6MA';
            ELSIF v_vertical = 'RTO' THEN
                v_did := '62569';
                v_template := 'MjA5ODg6Nzg6MA';
            ELSE
                v_did := '24398';
                v_template := 'MjA5ODc6Nzg6MA';
            END IF;
        ELSIF p_platform = 'mcg' THEN
            v_did := '+16415342633';
            v_template := NULL;
        ELSIF p_platform = 'twilio' THEN
            v_did := '+13237160750';
            v_template := NULL;
        ELSE
            v_did := NULL;
            v_template := NULL;
        END IF;

        SELECT scs.version, scs.message
        INTO v_version, v_message
        FROM subscriber.content_sms scs
        WHERE scs.vertical = v_vertical
          AND scs.content_type = p_content_type
        ORDER BY RANDOM()
        LIMIT 1;
        
        INSERT INTO subscriber.transaction_sms(phone, email, firstname, lastname, version, message, platform, campaign_date, did, template)
        VALUES (
            v_phone,
            v_email,
            v_firstname,
            v_lastname,
            v_version,
            CONCAT(v_brand_name, ': ', v_message, ' ', v_shorturl, '?A=', v_version,'&T=', to_hex(CAST(EXTRACT(EPOCH FROM CURRENT_DATE) AS INTEGER))),
            p_platform,
            CURRENT_DATE,
            v_did,
            v_template
        );
        
        v_count := v_count + 1;
    END LOOP;

    RETURN CONCAT('Successfully inserted ', v_count, ' row(s) into transaction_sms.');
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN CONCAT('Error: ', SQLERRM);
END;
$$
LANGUAGE plpgsql;
