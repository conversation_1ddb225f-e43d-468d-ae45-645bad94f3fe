import requests
from config import <PERSON><PERSON><PERSON><PERSON>OMAIN, MCE_CLIENT_ID, MC<PERSON>_CLIENT_SECRET, MCE_ACCOUNT_ID, MCE_AUTH_URL, MC<PERSON>_SEND_SMS_URL, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE
from datetime import datetime
import pytz
import psycopg2
from psycopg2.extras import Dict<PERSON>ursor
import concurrent.futures

domain = MCE_DOMAIN
client_id = MCE_CLIENT_ID
client_secret = MCE_CLIENT_SECRET
account_id = MCE_ACCOUNT_ID
auth_url = MCE_AUTH_URL
send_sms_url = MCE_SEND_SMS_URL
sms_send_id = 'MjA3Mjg6Nzg6MA'

def authenticate(client_id, client_secret):
    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'account_id': account_id
    }
    
    response = requests.post(auth_url, json=payload)
    if response.status_code != 200:
        print("Authentication Error:", response.text)
        raise Exception("Failed to authenticate with Salesforce Marketing Cloud API")
    
    return response.json().get('access_token')

def send_sms(access_token, phone_number, message):
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "Subscribe": True,
        "Resubscribe": True,
        "mobileNumbers": [phone_number],
        "keyword": "CC",
        "Override": True,
        "messageText": message
    }
    
    url = send_sms_url.format(sms_send_id=sms_send_id)
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 202:
        return {"status": "success", "message": f"{response.status_code} - {response.text}"}
    else:
        return {"status": "error", "message": f"{response.status_code} - {response.text}"}

def get_sms_data_from_db(chunk_size=100):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor(cursor_factory=DictCursor)

        query = """
            SELECT id_sms, phone, email, firstname, lastname, version, message
            FROM subscriber.transaction_sms
            WHERE platform = 'mce' AND campaign_date = CURRENT_DATE AND delivery_status IS NULL
        """
        cursor.execute(query)
        
        while True:
            records = cursor.fetchmany(chunk_size)
            if not records:
                break
            yield records

    except Exception as e:
        print("Error reading data from PostgreSQL table:", str(e))
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def update_delivery_status(id_sms, status, delivery_date, response_mce):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(user=PGSQL_USER, password=PGSQL_PASSWORD, host=PGSQL_HOST, database=PGSQL_DATABASE)
        cursor = connection.cursor()

        update_query = """
            UPDATE subscriber.transaction_sms
            SET delivery_status = %s, delivery_date = %s, response_mce = %s
            WHERE id_sms = %s
        """
        cursor.execute(update_query, (status, delivery_date, response_mce, id_sms))
        connection.commit()
    except Exception as e:
        print("Error updating delivery status in PostgreSQL table:", str(e))
    finally:
        if cursor is not None:
            cursor.close()
        if connection is not None:
            connection.close()

def process_sms(sms, access_token):
    id_sms = sms['id_sms']
    phone = sms['phone']
    message = sms['message']
    
    # Add 1 prefix to phone number
    formatted_phone = f"1{phone}"
    result = send_sms(access_token, formatted_phone, message)
    
    status = result['status']
    response_mce = result['message']
    delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific')) if status == "success" else None
    
    update_delivery_status(id_sms, status, delivery_date, response_mce)

def main():
    access_token = authenticate(client_id, client_secret)
    
    with concurrent.futures.ThreadPoolExecutor() as executor:
        for sms_chunk in get_sms_data_from_db():
            futures = [executor.submit(process_sms, sms, access_token) for sms in sms_chunk]
            concurrent.futures.wait(futures)

if __name__ == "__main__":
    main()